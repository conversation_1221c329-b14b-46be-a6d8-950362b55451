<template>
  <div style="height: 100%;">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="待审核" name="pending" />
      <el-tab-pane label="已审核" name="audited" />
    </el-tabs>

    <div class="wrap">
      <div class="cus-header">
        <el-form :model="searchForm" label-width="100px">
          <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
            <el-form-item label="项目名称">
              <el-input v-model="searchForm.projectName" placeholder="请输入项目名称" clearable />
            </el-form-item>

            <el-form-item label="项目编号">
              <el-input v-model="searchForm.projectCode" placeholder="请输入项目编号" clearable />
            </el-form-item>

            <el-form-item label="运维商">
              <el-input v-model="searchForm.companyName" placeholder="请输入公司名称" clearable />
            </el-form-item>

            <el-form-item v-if="showAuditFilters" label="审核状态">
              <el-select v-model="searchForm.auditStatus" placeholder="请选择审核状态" clearable style="width: 100%;">
                <el-option label="已通过" value="APPROVED" />
                <el-option label="已驳回" value="REJECTED" />
              </el-select>
            </el-form-item>

            <div class="search-buttons">
              <el-button type="default" @click="onReset">重置</el-button>
              <el-button type="primary" @click="queryList">查询</el-button>
              <el-link
                type="primary"
                :underline="false"
                style="margin-left: 10px;"
                @click="toggleExpand"
              >
                {{ isExpanded ? '收起' : '展开' }}
                <el-icon>
                  <arrow-up v-if="isExpanded" />
                  <arrow-down v-else />
                </el-icon>
              </el-link>
            </div>
          </div>
        </el-form>
      </div>

      <div class="cus-main">
        <div class="cus-list" v-loading="loading">
          <div style="text-align: right; margin-bottom: 16px;">
            <el-button type="info" plain @click="handleExport" :loading="exportLoading">导出</el-button>
          </div>

          <el-table :data="listArr" class="cus-table">
            <el-table-column type="index" label="序号" width="60" align="center" fixed />
            <el-table-column prop="projectName" label="招商项目名称" min-width="180" show-overflow-tooltip />
            <el-table-column prop="projectCode" label="招商项目编码" min-width="160" show-overflow-tooltip />
            <!-- <el-table-column label="项目状态" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="statusTagType(row.biddingStatus)" size="small">
                  {{ statusText(row.biddingStatus) }}
                </el-tag>
              </template>
            </el-table-column> -->
            <el-table-column label="区域子块" min-width="180" show-overflow-tooltip>
              <template #default="{ row }">
                {{ formatRegionNames(row) }}
              </template>
            </el-table-column>
            <el-table-column label="电站数量" width="140" align="center">
              <template #default="{ row }">
                {{ formatStationSummary(row) }}
              </template>
            </el-table-column>
            <el-table-column prop="companyName" label="运维商" min-width="220" show-overflow-tooltip />
            <el-table-column prop="releaseTime" label="发布时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.releaseTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="interactionDeadline" label="报名截止时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.interactionDeadline) }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="createdAt" label="报名时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column v-if="showAuditFilters" align="center" label="审核结果" width="120">
              <template #default="{ row }">
                <el-tag :type="auditTagType(row.auditStatus)" size="small">{{ auditStatusText(row.auditStatus) }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column v-if="showAuditFilters" align="center" prop="updatedAt" label="审核时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.updatedAt) }}
              </template>
            </el-table-column>
            <!-- <el-table-column label="发布人" prop="publishedBy" width="120" align="center">
              <template #default="{ row }">
                {{ row.publishedBy || row.createdBy || '-' }}
              </template>
            </el-table-column> -->
            <el-table-column label="操作" width="180" fixed="right" align="center">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleViewDetail(row)">查看</el-button>
                <el-button v-if="activeTab === 'pending'" type="success" link @click="handleAudit(row)">审核</el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-if="total"
            class="cus-pages"
            background
            layout="sizes, prev, pager, next, ->, total"
            :page-sizes="[10, 20, 30]"
            :page-size="pagination.pageSize"
            :current-page="pagination.pageNum"
            :total="total"
            @size-change="changeSize"
            @current-change="changeCurrent"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { useTablePagination } from '@/composables/useTablePagination'
import dayjs from 'dayjs'
import biddingApi from '@/api/bidding'
import { ElMessage, ElMessageBox } from 'element-plus'

const activeTab = ref('pending')
const isExpanded = ref(false)
const exportLoading = ref(false)
const router = useRouter()
const searchForm = reactive({
  projectName: '',
  projectCode: '',
  companyName: '',
  registrationCode: '',
  auditStatus: ''
})

const showAuditFilters = computed(() => activeTab.value === 'audited')

const buildQueryParams = () => {
  const params = {
    projectName: searchForm.projectName || undefined,
    projectCode: searchForm.projectCode || undefined,
    companyName: searchForm.companyName || undefined,
    registrationCode: searchForm.registrationCode || undefined
  }

  if (showAuditFilters.value && searchForm.auditStatus) {
    params.auditStatus = searchForm.auditStatus
  }

  return params
}

const fetchRegistrationPage = (params) => {
  const requestParams = { ...params }

  if (!showAuditFilters.value) {
    delete requestParams.auditStatus
  }

  const apiMethod = activeTab.value === 'pending'
    ? biddingApi.getPendingAuditBiddingRegistrationPage
    : biddingApi.getAuditedBiddingRegistrationPage

  return apiMethod(requestParams)
}

const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent
} = useTablePagination(fetchRegistrationPage, () => buildQueryParams(), { manual: true })

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

const onReset = () => {
  Object.assign(searchForm, {
    projectName: '',
    projectCode: '',
    companyName: '',
    registrationCode: '',
    auditStatus: ''
  })
  queryList()
}

const handleViewDetail = (row) => {
  if (!row?.registrationId) return
  router.push({
    name: 'biddingProjectRegistrationAudit',
    query: {
      registrationId: row.registrationId,
      projectId: row.projectId,
      mode: 'view'  // 只读模式
    }
  })
}

const handleAudit = (row) => {
  if (!row?.registrationId) return
  router.push({
    name: 'biddingProjectRegistrationAudit',
    query: {
      registrationId: row.registrationId,
      projectId: row.projectId
      // 不传 mode 参数,默认为审核模式
    }
  })
}

const formatDateTime = (value) => {
  if (!value) return '-'
  return value.replace('T', ' ')
}

const formatDate = (value) => {
  if (!value) return '-'
  return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
}

const formatRegionNames = (row) => {
  if (Array.isArray(row?.regionBlocks) && row.regionBlocks.length) {
    return row.regionBlocks.map(item => item.blockName).join('、')
  }
  if (row?.regionNames) {
    return row.regionNames
  }
  return '-'
}

const formatStationSummary = (row) => {
  if (Array.isArray(row?.regionBlocks) && row.regionBlocks.length) {
    const summary = row.regionBlocks.reduce((acc, block) => {
      const completed = Number(block.completedStationCount) || 0
      const unfinished = Number(block.unfinishedStationCount) || 0
      const total = Number(block.stationCount) || completed + unfinished
      return {
        completed: acc.completed + completed,
        unfinished: acc.unfinished + unfinished,
        total: acc.total + total
      }
    }, { completed: 0, unfinished: 0, total: 0 })
    return `完成${summary.completed} / 未完成${summary.unfinished} · 共${summary.total}`
  }
  if (typeof row?.stationCount !== 'undefined') {
    return row.stationCount
  }
  return '-'
}

const statusText = (status) => {
  const map = {
    INTERACTING: '互动中',
    RESPONDING: '响应中',
    REVIEWING: '评审中',
    COMPLETED: '已完成',
    ABANDONED: '已废弃',
    TERMINATED: '已中止'
  }
  return map[status] || status || '-'
}

const statusTagType = (status) => {
  const typeMap = {
    INTERACTING: 'primary',
    RESPONDING: 'success',
    REVIEWING: 'warning',
    COMPLETED: 'success',
    ABANDONED: 'info',
    TERMINATED: 'danger'
  }
  return typeMap[status] || 'info'
}

const auditStatusText = (status) => {
  if (status === 'APPROVED') return '已通过'
  if (status === 'REJECTED') return '已驳回'
  if (status === 'PENDING') return '待审核'
  return '-'
}

const auditTagType = (status) => {
  if (status === 'APPROVED') return 'success'
  if (status === 'REJECTED') return 'danger'
  return 'info'
}

watch(activeTab, (tab) => {
  if (tab === 'pending') {
    searchForm.auditStatus = ''
  }
  queryList()
})

onActivated(() => {
  getList()
})

// 导出处理
const handleExport = async () => {
  exportLoading.value = true;
  try {
    const params = { ...searchForm };
    const blob = await biddingApi.exportBiddingRegistrations(params);
    if (!blob) {
      throw new Error('导出失败');
    }
    const binaryData = [blob];
    const link = document.createElement('a');
    link.style.display = 'none';
    const now = new Date();
    const pad = (num) => String(num).padStart(2, '0');
    const timestamp = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
    const downloadUrl = window.URL.createObjectURL(new Blob(binaryData));
    link.href = downloadUrl;
    link.setAttribute('download', `招商报名列表_${timestamp}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出招商报名列表失败', error);
    ElMessage.error(error?.message || '导出失败');
  } finally {
    exportLoading.value = false;
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

:deep(.el-tabs__nav-scroll) {
  background-color: var(--el-bg-color);
  padding-left: 12px;
}

.wrap {
  height: calc(100% - 54px);
}

.cus-header {
  margin-bottom: 0;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    width: 100%;

    /* 默认状态（收起）- 只显示前两个元素和按钮组 */
    .el-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    /* 展开状态 - 显示所有元素 */
    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  .el-form-item {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;

  > .el-button {
    margin-bottom: 10px;
  }

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }

    .cus-pages {
      margin-top: 10px;
    }
  }
}
</style>
