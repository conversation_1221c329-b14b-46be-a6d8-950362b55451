<template>
  <div class="wrap">
    <div class="cus-header">
      <el-form :model="formSearch" label-width="100px">
        <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
          <el-form-item label="招商项目名称">
            <el-input v-model="formSearch.projectName" placeholder="请输入项目名称" clearable />
          </el-form-item>
          <el-form-item label="招商项目编码">
            <el-input v-model="formSearch.projectCode" placeholder="请输入项目编码" clearable />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="formSearch.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="item in biddingStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="区域子块">
            <el-input v-model="formSearch.region" placeholder="支持名称模糊查询" clearable />
          </el-form-item>
          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-link type="primary" :underline="false" style="margin-left: 10px;" @click="toggleExpand">
              {{ isExpanded ? '收起' : '展开' }}
              <el-icon>
                <arrow-up v-if="isExpanded" />
                <arrow-down v-else />
              </el-icon>
            </el-link>
          </div>
        </div>
      </el-form>
    </div>

    <div class="cus-main">
      <div class="cus-list" v-loading="loading">
        <div class="list-actions">
          <el-button type="info" plain @click="handleExport" :loading="exportLoading">导出</el-button>
          <el-button type="success" plain @click="handleAdd">新增招商项目</el-button>
        </div>
        <el-table :data="listArr" class="cus-table">
          <el-table-column type="index" label="序号" width="60" align="center" fixed />
          <el-table-column prop="projectName" label="招商项目名称" min-width="180" show-overflow-tooltip />
          <el-table-column prop="projectCode" label="招商项目编码" min-width="160" show-overflow-tooltip />
          <el-table-column label="区域子块" min-width="220">
            <template #default="{ row }">
              <span>
                {{ formatRegionNames(row) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="电站数量" width="140" align="center">
            <template #default="{ row }">
              {{ formatStationSummary(row) }}
            </template>
          </el-table-column>
          <el-table-column prop="releaseTime" label="发布时间" width="180" align="center">
            <template #default="{ row }">
              {{ formatDate(row.releaseTime) }}
            </template>
          </el-table-column>
          <el-table-column prop="interactionDeadline" label="报名截止时间" width="180" align="center">
            <template #default="{ row }">
              {{ formatDate(row.interactionDeadline) }}
            </template>
          </el-table-column>
          <el-table-column prop="reviewStartTime" label="评审开始时间" width="180" align="center">
            <template #default="{ row }">
              {{ formatDate(row.reviewStartTime) }}
            </template>
          </el-table-column>
          <el-table-column label="发布人" prop="publisher" width="120" align="center">
            <template #default="{ row }">
              {{ row.publishedBy || row.createdBy || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="110" align="center">
            <template #default="{ row }">
              <el-tag :type="statusTagType(row.status)" size="small">{{ statusText(row.status) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" fixed="right" align="center">
            <template #default="{ row }">
              <el-button type="primary" link :disabled="!['DRAFT', 'SUBMITTED'].includes(row.status)" @click="handleEdit(row)">编辑</el-button>
              <el-button type="danger" link :disabled="!['DRAFT', 'SUBMITTED'].includes(row.status)" @click="handleDelete(row)">删除</el-button>
              <el-button type="primary" link @click="handleCopy(row)">复制</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="total"
          class="cus-pages"
          background
          layout="sizes, prev, pager, next, ->, total"
          :page-sizes="[10, 20, 30]"
          :page-size="pagination.pageSize"
          :current-page="pagination.pageNum"
          :total="total"
          @size-change="changeSize"
          @current-change="changeCurrent"
        />
      </div>
    </div>

    <OperateWizard
      v-model:visible="operateVisible"
      :mode="operateMode"
      :project-id="currentProjectId"
      @success="handleOperateSuccess"
    />
  </div>
</template>

<script setup>
import { reactive, ref, onActivated, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import biddingApi from '@/api/bidding'
import { useTablePagination } from '@/composables/useTablePagination'
import { useDictStore } from '@/stores/modules/dict'
import OperateWizard from '../components/OperateWizard.vue'

const isExpanded = ref(false)
const exportLoading = ref(false)
const formSearch = reactive({
  projectName: '',
  projectCode: '',
  status: '',
  region: ''
})

const operateVisible = ref(false)
const operateMode = ref('create')
const currentProjectId = ref(null)
const dictStore = useDictStore()
const biddingStatusList = computed(() => dictStore.getDictByType('hds_bidding_status').filter(item => !['REVIEWING','RESPONDING','INTERACTING'].includes(item.value)))
const biddingStatusMap = computed(() => dictStore.getDictMapByType('hds_bidding_status'))

const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent
} = useTablePagination(
  biddingApi.getBiddingProjectPage,
  () => ({
    projectName: formSearch.projectName || undefined,
    projectCode: formSearch.projectCode || undefined,
    region: formSearch.region || undefined,
    statusList: formSearch.status ? [formSearch.status] : undefined
  })
)

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

const onReset = () => {
  formSearch.projectName = ''
  formSearch.projectCode = ''
  formSearch.status = ''
  formSearch.region = ''
  queryList()
}

const handleAdd = () => {
  operateMode.value = 'create'
  currentProjectId.value = null
  operateVisible.value = true
}

const handleEdit = (row) => {
  operateMode.value = 'edit'
  currentProjectId.value = row.id
  operateVisible.value = true
}

const handleCopy = (row) => {
  operateMode.value = 'copy'
  currentProjectId.value = row.id
  operateVisible.value = true
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确认删除【${row.projectName || ''}】?`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    const res = await biddingApi.deleteBiddingProject(row.id)
    if (res.data?.success) {
      ElMessage.success('删除成功')
      getList()
    } else {
      ElMessage.error(res.data?.error || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleOperateSuccess = () => {
  operateVisible.value = false
  getList()
}

const formatDate = (value) => {
  if (!value) return '-'
  return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
}

const formatRegionNames = (row) => {
  if (Array.isArray(row?.regionBlocks) && row.regionBlocks.length) {
    return row.regionBlocks.map(item => item.blockName).join('、')
  }
  if (row?.regionNames) {
    return row.regionNames
  }
  return '-'
}

const formatStationSummary = (row) => {
  if (Array.isArray(row?.regionBlocks) && row.regionBlocks.length) {
    const summary = row.regionBlocks.reduce((acc, block) => {
      const completed = Number(block.completedStationCount) || 0
      const unfinished = Number(block.unfinishedStationCount) || 0
      const total = Number(block.stationCount) || completed + unfinished
      return {
        completed: acc.completed + completed,
        unfinished: acc.unfinished + unfinished,
        total: acc.total + total
      }
    }, { completed: 0, unfinished: 0, total: 0 })
    return `完成${summary.completed} / 未完成${summary.unfinished} · 共${summary.total}`
  }
  if (typeof row?.stationCount !== 'undefined') {
    return row.stationCount
  }
  return '-'
}

const statusText = (status) => biddingStatusMap.value[status] || status || '-'

const statusTagType = (status) => {
  const typeMap = {
    DRAFT: 'info',
    SUBMITTED: 'warning',
    PUBLISHED: 'success',
    INTERACTING: 'primary',
    REVIEWING: 'warning',
    COMPLETED: 'success',
    ABANDONED: 'info',
  }
  return typeMap[status] || 'info'
}

onActivated(() => {
  getList()
})

onMounted(() => {
  dictStore.fetchDict(['hds_bidding_status'])
})

// 导出处理
const handleExport = async () => {
  exportLoading.value = true;
  try {
    const params = { ...formSearch };
    const blob = await biddingApi.exportBiddingManagementProjects(params);
    if (!blob) {
      throw new Error('导出失败');
    }
    const binaryData = [blob];
    const link = document.createElement('a');
    link.style.display = 'none';
    const now = new Date();
    const pad = (num) => String(num).padStart(2, '0');
    const timestamp = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
    const downloadUrl = window.URL.createObjectURL(new Blob(binaryData));
    link.href = downloadUrl;
    link.setAttribute('download', `招商管理项目_${timestamp}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出招商管理项目失败', error);
    ElMessage.error(error?.message || '导出失败');
  } finally {
    exportLoading.value = false;
  }
}
</script>

<style scoped lang="less">
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

.wrap {
  height: 100%;
}

.list-actions {
  text-align: right;
  margin-bottom: 12px;
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;

    .el-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  :deep(.el-form-item) {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  height: 100%;

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    // 表格容器自动填充剩余空间
    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }
  }
}
</style>
