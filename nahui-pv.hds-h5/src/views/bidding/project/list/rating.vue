<template>
  <div style="height: 100%;">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="待评分" name="pending" />
      <el-tab-pane label="已评分" name="completed" />
    </el-tabs>

    <div class="wrap">
      <div class="cus-header">
        <el-form :model="formSearch" label-width="100px">
          <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
            <el-form-item label="招商项目名称">
              <el-input v-model="formSearch.projectName" placeholder="请输入项目名称" clearable />
            </el-form-item>
            <el-form-item label="招商项目编码">
              <el-input v-model="formSearch.projectCode" placeholder="请输入项目编码" clearable />
            </el-form-item>
            <!-- <el-form-item label="项目状态">
              <el-select v-model="formSearch.status" placeholder="请选择状态" clearable>
                <el-option label="草稿" value="DRAFT" />
                <el-option label="已提交" value="SUBMITTED" />
                <el-option label="已发布" value="PUBLISHED" />
                <el-option label="已关闭" value="CLOSED" />
                <el-option label="已废弃" value="ABANDONED" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="区域子块">
              <el-input v-model="formSearch.regionBlock" placeholder="支持名称模糊查询" clearable />
            </el-form-item>
            <div class="search-buttons">
              <el-button type="default" @click="onReset">重置</el-button>
              <el-button type="primary" @click="queryList">查询</el-button>
              <el-link type="primary" :underline="false" style="margin-left: 10px;" @click="toggleExpand">
                {{ isExpanded ? '收起' : '展开' }}
                <el-icon>
                  <arrow-up v-if="isExpanded" />
                  <arrow-down v-else />
                </el-icon>
              </el-link>
            </div>
          </div>
        </el-form>
      </div>

      <div class="cus-main">
        <div class="cus-list" v-loading="loading">
          <div style="text-align: right; margin-bottom: 16px;">
            <el-button type="info" plain @click="handleExport" :loading="exportLoading">导出</el-button>
          </div>

          <el-table :data="listArr" class="cus-table">
            <el-table-column type="index" label="序号" width="60" align="center" fixed />
            <el-table-column prop="projectName" label="招商项目名称" min-width="180" show-overflow-tooltip />
            <el-table-column prop="projectCode" label="招商项目编码" min-width="160" show-overflow-tooltip />
            <!-- <el-table-column label="项目状态" width="120" align="center">
              <template #default="{ row }">
                <el-tag :type="statusTagType(row.status)" size="small">
                  {{ statusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column> -->
            <el-table-column label="区域子块" min-width="180" show-overflow-tooltip>
              <template #default="{ row }">
                {{ formatRegionNames(row) }}
              </template>
            </el-table-column>
            <el-table-column label="电站数量" width="140" align="center">
              <template #default="{ row }">
                {{ formatStationSummary(row) }}
              </template>
            </el-table-column>
            <el-table-column prop="releaseTime" label="发布时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.releaseTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="interactionDeadline" label="报名截止时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.interactionDeadline) }}
              </template>
            </el-table-column>
            <el-table-column prop="reviewStartTime" label="评审时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.reviewStartTime) }}
              </template>
            </el-table-column>
            <el-table-column label="评审进度" width="150" align="center">
              <template #default="{ row }">
                {{ formatRatingProgress(row) }}
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>
            <!-- <el-table-column label="发布人" prop="publishedBy" width="120" align="center">
              <template #default="{ row }">
                {{ row.publishedBy || row.createdBy || '-' }}
              </template>
            </el-table-column> -->
            <el-table-column label="操作" width="180" fixed="right" align="center">
              <template #default="{ row }">
                <el-button type="primary" link @click="handleRate(row)">
                  {{ activeTab === 'pending' ? '开始评分' : '查看评分' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-if="total"
            class="cus-pages"
            background
            layout="sizes, prev, pager, next, ->, total"
            :page-sizes="[10, 20, 30]"
            :page-size="pagination.pageSize"
            :current-page="pagination.pageNum"
            :total="total"
            @size-change="changeSize"
            @current-change="changeCurrent"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onActivated, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import bidding from '@/api/bidding.js'
import { useTablePagination } from '@/composables/useTablePagination'
import { ElMessage, ElMessageBox } from 'element-plus'

const activeTab = ref('pending')
const isExpanded = ref(false)
const exportLoading = ref(false)
const formSearch = reactive({
  projectName: '',
  projectCode: '',
  status: '',
  regionBlock: ''
})

const router = useRouter()

const buildQueryParams = () => {
  return {
    projectName: formSearch.projectName || undefined,
    projectCode: formSearch.projectCode || undefined,
    region: formSearch.regionBlock || undefined,
    statusList: formSearch.status ? [formSearch.status] : undefined
  }
}

const fetchRatingProjects = (params) => {
  // 根据 tab 调用不同的接口
  const apiMethod = activeTab.value === 'pending'
    ? bidding.getPendingRatingProjects
    : bidding.getCompletedRatingProjects

  return apiMethod(params)
}

const {
  loading,
  listArr,
  total,
  pagination,
  getList,
  queryList,
  changeSize,
  changeCurrent
} = useTablePagination(fetchRatingProjects, () => buildQueryParams(), { manual: true })

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

const onReset = () => {
  formSearch.projectName = ''
  formSearch.projectCode = ''
  formSearch.status = ''
  formSearch.regionBlock = ''
  queryList()
}

const handleRate = (row) => {
  // 跳转到评分页面：待评分可编辑；已评分只读查看
  const query = { projectId: row.id }
  if (activeTab.value === 'completed') {
    query.readonly = '1'
    query.status = 'SUBMITTED'
  }
  router.push({ name: 'biddingProjectRatingForm', query })
}

const formatDate = (value) => {
  if (!value) return '-'
  return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
}

const formatRegionNames = (row) => {
  if (Array.isArray(row?.regionBlocks) && row.regionBlocks.length) {
    return row.regionBlocks.map(item => item.blockName).join('、')
  }
  if (row?.regionNames) {
    return row.regionNames
  }
  return '-'
}

const formatStationSummary = (row) => {
  if (Array.isArray(row?.regionBlocks) && row.regionBlocks.length) {
    const summary = row.regionBlocks.reduce((acc, block) => {
      const completed = Number(block.completedStationCount) || 0
      const unfinished = Number(block.unfinishedStationCount) || 0
      const total = Number(block.stationCount) || completed + unfinished
      return {
        completed: acc.completed + completed,
        unfinished: acc.unfinished + unfinished,
        total: acc.total + total
      }
    }, { completed: 0, unfinished: 0, total: 0 })
    return `完成${summary.completed} / 未完成${summary.unfinished} · 共${summary.total}`
  }
  if (typeof row?.stationCount !== 'undefined') {
    return row.stationCount
  }
  return '-'
}

const formatRatingProgress = (row) => {
  const submitted = row.submittedEvaluators ?? 0
  const total = row.totalEvaluators ?? 0
  return `${submitted}/${total}`
}

const statusText = (status) => {
  const map = {
    DRAFT: '草稿',
    SUBMITTED: '待发布',
    PUBLISHED: '已发布',
    INTERACTING: '互动中',
    // RESPONDING: '响应中',
    REVIEWING: '评审中',
    COMPLETED: '已完成',
    ABANDONED: '已废弃',
    TERMINATED: '已中止'
  }
  return map[status] || status || '-'
}

const statusTagType = (status) => {
  const typeMap = {
    DRAFT: 'info',
    SUBMITTED: 'warning',
    PUBLISHED: 'success',
    INTERACTING: 'primary',
    RESPONDING: 'success',
    REVIEWING: 'warning',
    COMPLETED: 'success',
    ABANDONED: 'info',
    TERMINATED: 'danger'
  }
  return typeMap[status] || 'info'
}

watch(activeTab, () => {
  queryList()
})

onActivated(() => {
  getList()
})

// 导出处理
const handleExport = async () => {
  exportLoading.value = true;
  try {
    const params = { ...formSearch };
    const blob = await bidding.exportBiddingRatingProjects(params);
    if (!blob) {
      throw new Error('导出失败');
    }
    const binaryData = [blob];
    const link = document.createElement('a');
    link.style.display = 'none';
    const now = new Date();
    const pad = (num) => String(num).padStart(2, '0');
    const timestamp = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
    const downloadUrl = window.URL.createObjectURL(new Blob(binaryData));
    link.href = downloadUrl;
    link.setAttribute('download', `招商评分项目_${timestamp}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出招商评分项目失败', error);
    ElMessage.error(error?.message || '导出失败');
  } finally {
    exportLoading.value = false;
  }
}
</script>

<style scoped lang="less">
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

:deep(.el-tabs__nav-scroll) {
  background-color: var(--el-bg-color);
  padding-left: 12px;
}

.wrap {
  height: calc(100% - 54px);
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;

    .el-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  :deep(.el-form-item) {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  height: 100%;

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    // 表格容器自动填充剩余空间
    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }
  }
}
</style>
