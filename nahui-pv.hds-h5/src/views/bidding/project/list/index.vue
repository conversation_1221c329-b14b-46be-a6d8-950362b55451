<template>
  <div style="height: 100%;">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="互动中" name="INTERACTING" />
      <!-- <el-tab-pane label="响应中" name="RESPONDING" /> -->
      <el-tab-pane label="评审中" name="REVIEWING" />
      <el-tab-pane label="已完成" name="COMPLETED" />
      <el-tab-pane label="已废弃" name="ABANDONED" />
      <!-- <el-tab-pane label="已中止" name="TERMINATED" /> -->
      <el-tab-pane label="全部" name="ALL" />
    </el-tabs>

    <div class="wrap">
      <div class="cus-header">
      <el-form :model="formSearch" label-width="100px">
        <div class="form-item-grid" :class="[isExpanded ? 'is-expanded' : '']">
          <el-form-item label="招商项目名称">
            <el-input v-model="formSearch.projectName" placeholder="请输入项目名称" clearable />
          </el-form-item>
          <el-form-item label="招商项目编码">
            <el-input v-model="formSearch.projectCode" placeholder="请输入项目编码" clearable />
          </el-form-item>
          <el-form-item label="区域子块">
            <el-input v-model="formSearch.regionBlock" placeholder="支持名称模糊查询" clearable />
          </el-form-item>
          <div class="search-buttons">
            <el-button type="default" @click="onReset">重置</el-button>
            <el-button type="primary" @click="queryList">查询</el-button>
            <el-link type="primary" :underline="false" style="margin-left: 10px;" @click="toggleExpand">
              {{ isExpanded ? '收起' : '展开' }}
              <el-icon>
                <arrow-up v-if="isExpanded" />
                <arrow-down v-else />
              </el-icon>
            </el-link>
          </div>
        </div>
      </el-form>
    </div>

    <div class="cus-main">
      <div class="cus-list" v-loading="loading">
        <div style="text-align: right; margin-bottom: 16px;">
          <el-button type="info" plain @click="handleExport" :loading="exportLoading">导出</el-button>
        </div>

        <el-table :data="listArr" class="cus-table">
          <el-table-column type="index" label="序号" width="60" align="center" fixed />
          <el-table-column prop="projectName" label="招商项目名称" min-width="220" show-overflow-tooltip />
          <el-table-column prop="projectCode" label="招商项目编码" min-width="160" show-overflow-tooltip />
          <el-table-column label="招商状态" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="tagType(displayStatus(row))" size="small">{{ displayStatusText(row) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="区域子块" min-width="220" show-overflow-tooltip>
            <template #default="{ row }">{{ formatRegionBlocks(row) }}</template>
          </el-table-column>
          <el-table-column prop="stationCount" label="电站数量" width="120" align="center" />
          <el-table-column prop="releaseTime" label="发布时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.releaseTime) }}
              </template>
            </el-table-column>
            <el-table-column prop="interactionDeadline" label="报名截止时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.interactionDeadline) }}
              </template>
            </el-table-column>
            <el-table-column v-if="['REVIEWING','ALL'].includes(activeTab)" prop="reviewStartTime" label="评审时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.reviewStartTime) }}
              </template>
            </el-table-column>
            <el-table-column v-if="['REVIEWING','ALL'].includes(activeTab)" label="评审进度" width="150" align="center">
              <template #default="{ row }">
                {{ formatRatingProgress(row) }}
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" width="180" align="center">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>
            <el-table-column label="发布人" prop="publishedBy" width="120" align="center">
              <template #default="{ row }">
                {{ row.publishedBy || row.createdBy || '-' }}
              </template>
            </el-table-column>
          <el-table-column fixed="right" label="操作" width="220" align="center">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleView(row)">查看</el-button>
              <el-button link type="primary" v-if="['INTERACTING'].includes(row.biddingStatus)" @click="handleDelay(row)">延期</el-button>
              <el-button link type="success" v-if="canConfirm(row)" @click="handleConfirm(row)">确认</el-button>
              <el-button link type="danger" v-if="['INTERACTING', 'RESPONDING', 'REVIEWING'].includes(row.biddingStatus)"  @click="handleAbandon(row)">废弃</el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-if="total"
          class="cus-pages"
          background
          layout="sizes, prev, pager, next, ->, total"
          :page-sizes="[10, 20, 30]"
          :page-size="pagination.pageSize"
          :current-page="pagination.pageNum"
          :total="total"
          @size-change="changeSize"
          @current-change="changeCurrent"
        />
      </div>
    </div>

    <Delay v-model:visible="delayVisible" :data="delayData" @success="handleDelaySuccess" />
    <AbandonDialog
      v-model:visible="abandonVisible"
      :project-id="abandonProjectId"
      :reasons="abandonReasonOptions"
      @success="handleAbandonSuccess"
    />
  </div>
</div>
</template>

<script setup>
import { ref, reactive, watch, onActivated, computed, onMounted } from 'vue'
import AbandonDialog from '../components/AbandonDialog.vue'
import { useRouter } from 'vue-router'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import bidding from '@/api/bidding'
import { useTablePagination } from '@/composables/useTablePagination'
import Delay from '../components/Delay.vue'
import { useDictStore } from '@/stores/modules/dict'
import { ElMessage, ElMessageBox } from 'element-plus'

const router = useRouter()
const activeTab = ref('INTERACTING')
const isExpanded = ref(false)
const formSearch = reactive({ projectName: '', projectCode: '', regionBlock: '' })
const exportLoading = ref(false)

// 延期相关
const delayVisible = ref(false)
const delayData = ref({})
const dictStore = useDictStore()
const abandonReasonOptions = computed(() => dictStore.getDictByType('bidding_abandon_reason'))
const biddingStatusMap = computed(() => dictStore.getDictMapByType('hds_bidding_status'))

onMounted(() => {
  dictStore.fetchDict(['bidding_abandon_reason', 'hds_bidding_status'])
})

const toggleExpand = () => { isExpanded.value = !isExpanded.value }
const onReset = () => { formSearch.projectName = formSearch.projectCode = formSearch.regionBlock = ''; queryList() }

const buildQueryParams = () => {
  let biddingStatus
  if (activeTab.value === 'ALL') {
    biddingStatus = undefined
  } else {
    biddingStatus = [activeTab.value]
  }
  return {
    projectName: formSearch.projectName || undefined,
    projectCode: formSearch.projectCode || undefined,
    region: formSearch.regionBlock || undefined,
    biddingStatus
  }
}

// 状态显示
const displayStatus = (row) => {
  return (row?.biddingStatus || '').toUpperCase()
}

const displayStatusText = (row) => {
  const status = displayStatus(row)
  return biddingStatusMap.value[status] || status || '-'
}

const tagType = (s) => {
  const typeMap = {
    DRAFT: 'info',
    SUBMITTED: 'warning',
    PUBLISHED: 'success',
    INTERACTING: 'primary',
    RESPONDING: 'success',
    REVIEWING: 'warning',
    COMPLETED: 'success',
    ABANDONED: 'info',
    TERMINATED: 'danger'
  }
  return typeMap[s] || 'info'
}

// 列格式化
const formatRegionBlocks = (row) => Array.isArray(row?.regionBlocks) && row.regionBlocks.length ? row.regionBlocks.map(b => b.blockName).join('、') : (row?.regionNames || '-')

const formatRatingProgress = (row) => {
  const submitted = row.reviewSubmittedEvaluators ?? 0
  const total = row.reviewTotalEvaluators ?? 0
  return `${submitted}/${total}`
}

const formatDate = (value) => {
  if (!value) return '-'
  return dayjs(value).format('YYYY-MM-DD HH:mm:ss')
}

const fetchPage = (params) => bidding.getManagementBiddingProjectPage(params)
const { loading, listArr, total, pagination, getList, queryList, changeSize, changeCurrent } = useTablePagination(fetchPage, () => buildQueryParams(), { manual: true })

watch(activeTab, () => { pagination.pageNum = 1; getList() })
onActivated(() => { getList() })

const handleView = (row) => {
  const phase = displayStatus(row)
  const query = { projectId: row.id, phase }
  if (row.status) query.status = row.status
  router.push({ name: 'biddingProjectManageDetail', query })
}

const handleDelay = (row) => {
  delayData.value = row
  delayVisible.value = true
}

const handleDelaySuccess = () => {
  getList()
}

// 判断是否可以确认（评审中且所有评审人都已提交）
const canConfirm = (row) => {
  if (row.biddingStatus !== 'REVIEWING') return false
  const total = row.reviewTotalEvaluators ?? 0
  const submitted = row.reviewSubmittedEvaluators ?? 0
  return total > 0 && submitted === total
}

// 确认评分结果
const handleConfirm = (row) => {
  router.push({
    name: 'biddingRatingConfirm',
    query: { projectId: row.id }
  })
}

// 废弃相关
const abandonVisible = ref(false)
const abandonProjectId = ref(null)
const handleAbandon = (row) => {
  abandonProjectId.value = row.id
  abandonVisible.value = true
}
const handleAbandonSuccess = () => {
  getList()
}

// 导出处理
const handleExport = async () => {
  exportLoading.value = true;
  try {
    const params = { ...formSearch };
    const blob = await bidding.exportMyBiddingProjects(params);
    if (!blob) {
      throw new Error('导出失败');
    }
    const binaryData = [blob];
    const link = document.createElement('a');
    link.style.display = 'none';
    const now = new Date();
    const pad = (num) => String(num).padStart(2, '0');
    const timestamp = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
    const downloadUrl = window.URL.createObjectURL(new Blob(binaryData));
    link.href = downloadUrl;
    link.setAttribute('download', `我的招商项目_${timestamp}.xlsx`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出我的招商项目失败', error);
    ElMessage.error(error?.message || '导出失败');
  } finally {
    exportLoading.value = false;
  }
}
</script>

<style scoped lang="less">
@import '@/assets/style/_cus_header.less';
@import '@/assets/style/_cus_list.less';

:deep(.el-tabs__nav-scroll) {
  background-color: var(--el-bg-color);
  padding-left: 12px;
}

.wrap {
  height: calc(100% - 54px);
}

.cus-header {
  margin-bottom: 0px;

  .form-item-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;

    .el-form-item:nth-child(n+3):not(:last-child) {
      display: none;
    }

    &.is-expanded {
      .el-form-item:nth-child(n+3):not(:last-child) {
        display: flex;
      }
    }
  }

  .search-buttons {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    grid-column: -2 / -1;
  }

  :deep(.el-form-item) {
    width: 100%;
    margin-bottom: 0px;

    .el-input,
    .el-select,
    .el-date-editor {
      width: 100%;
    }
  }
}

.cus-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  height: 100%;

  .cus-list {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    position: relative;

    // 表格容器自动填充剩余空间
    .cus-table {
      flex: 1;
      overflow: auto;
      height: 100%;
    }
  }
}
</style>
